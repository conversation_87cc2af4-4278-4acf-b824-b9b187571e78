<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import axios from 'axios'

interface Transaction {
  id: number
  amount: number
  amount_formatted: number
  balance_before: number
  balance_before_formatted: number
  balance_after: number
  balance_after_formatted: number
  type: string
  reference_id: string | null
  meta: any
  currency: {
    id: number
    code: string
    symbol: string
    decimals: number
  }
  created_at: string
}

interface PaginationInfo {
  limit: number
  offset: number
  total: number
}

// Remove props since we'll manage filters internally
// const props = defineProps<{
//   filters: FilterOptions
// }>()

// Internal filter management
const filters = ref({
  dateRange: 'all',
  typeFilter: 'all',
  gameFilter: '',
  currencyFilter: ''
})

const transactions = ref<Transaction[]>([])
const pagination = ref<PaginationInfo>({
  limit: 10,
  offset: 0,
  total: 0
})
const isLoading = ref(false)
const error = ref('')

// Filter options
const dateRangeOptions = [
  { value: 'all', label: 'All Time', shortLabel: 'all' },
  { value: '1h', label: 'Last Hour', shortLabel: '1h' },
  { value: '1d', label: 'Last Day', shortLabel: '1d' },
  { value: '30d', label: 'Last 30 Days', shortLabel: '30d' },
]

const typeFilterOptions = [
  { value: 'all', label: 'All Types', shortLabel: 'all' },
  { value: 'deposits', label: 'Deposits', shortLabel: 'deposits' },
  { value: 'withdrawals', label: 'Withdrawals', shortLabel: 'withdrawals' },
  { value: 'bets', label: 'Bets', shortLabel: 'bets' },
  { value: 'wins', label: 'Wins', shortLabel: 'wins' },
]

const gameFilterOptions = [
  { value: '', label: 'All Games', shortLabel: 'all' },
  { value: 'dice', label: 'Dice', shortLabel: 'dice' },
]

const currencyFilterOptions = [
  { value: '', label: 'All Currencies', shortLabel: 'all' },
  { value: 'USD', label: 'USD', shortLabel: 'USD' },
  { value: 'USDT', label: 'USDT', shortLabel: 'USDT' },
  { value: 'POINTS', label: 'POINTS', shortLabel: 'PTS' },
]

const currentPage = computed(() => Math.floor(pagination.value.offset / pagination.value.limit) + 1)
const totalPages = computed(() => Math.ceil(pagination.value.total / pagination.value.limit))

// Generate page numbers for pagination
const pageNumbers = computed(() => {
  const current = currentPage.value
  const total = totalPages.value
  const pages = []

  if (total <= 7) {
    // Show all pages if 7 or fewer
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Always show first page
    pages.push(1)

    if (current > 4) {
      pages.push('...')
    }

    // Show pages around current
    const start = Math.max(2, current - 1)
    const end = Math.min(total - 1, current + 1)

    for (let i = start; i <= end; i++) {
      if (i !== 1 && i !== total) {
        pages.push(i)
      }
    }

    if (current < total - 3) {
      pages.push('...')
    }

    // Always show last page
    if (total > 1) {
      pages.push(total)
    }
  }

  return pages
})

const fetchTransactions = async (resetOffset = false) => {
  isLoading.value = true
  error.value = ''

  if (resetOffset) {
    pagination.value.offset = 0
  }

  try {
    const params = new URLSearchParams({
      limit: pagination.value.limit.toString(),
      offset: pagination.value.offset.toString(),
      date_range: filters.value.dateRange,
      type_filter: filters.value.typeFilter,
    })

    if (filters.value.gameFilter) {
      params.append('game_filter', filters.value.gameFilter)
    }

    if (filters.value.currencyFilter) {
      params.append('currency_filter', filters.value.currencyFilter)
    }

    const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/transactions?${params}`)

    if (response.data.success) {
      transactions.value = response.data.data
      pagination.value = response.data.pagination
    } else {
      error.value = 'Failed to load transactions'
    }
  } catch (err: any) {
    console.error('Failed to fetch transactions:', err)
    error.value = 'Failed to load transactions'
  } finally {
    isLoading.value = false
  }
}

// Watch for filter changes
watch(filters, () => {
  fetchTransactions(true)
}, { deep: true, immediate: true })

const goToPage = (page: number) => {
  pagination.value.offset = (page - 1) * pagination.value.limit
  fetchTransactions()
}

const goToFirstPage = () => {
  goToPage(1)
}

const goToLastPage = () => {
  goToPage(totalPages.value)
}

const goToPreviousPage = () => {
  if (currentPage.value > 1) {
    goToPage(currentPage.value - 1)
  }
}

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    goToPage(currentPage.value + 1)
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString()
}

const getTransactionTypeClass = (type: string) => {
  if (type.includes('deposit') || type.includes('win')) {
    return 'positive'
  } else if (type.includes('withdrawal') || type.includes('bet')) {
    return 'negative'
  }
  return 'neutral'
}

const formatAmount = (amount: number, symbol: string) => {
  const sign = amount >= 0 ? '+' : ''
  return `${sign}${symbol}${Math.abs(amount).toFixed(2)}`
}


</script>

<template>
  <div class="transaction-list">
    <!-- Integrated Filters -->
    <div class="filters-section">

      <div class="filters-grid">
        <div class="filter-item time-filter">
          <label>Time Period</label>
          <div class="time-toggle-group">
            <button
              v-for="option in dateRangeOptions"
              :key="option.value"
              @click="filters.dateRange = option.value"
              :class="['time-toggle', { active: filters.dateRange === option.value }]"
            >
              {{ option.shortLabel }}
            </button>
          </div>
        </div>

        <div class="filter-item type-filter">
          <label>Type</label>
          <div class="toggle-group">
            <button
              v-for="option in typeFilterOptions"
              :key="option.value"
              @click="filters.typeFilter = option.value"
              :class="['toggle-btn', { active: filters.typeFilter === option.value }]"
            >
              {{ option.shortLabel }}
            </button>
          </div>
        </div>

        <div class="filter-item game-filter">
          <label>Game</label>
          <div class="toggle-group">
            <button
              v-for="option in gameFilterOptions"
              :key="option.value"
              @click="filters.gameFilter = option.value"
              :class="['toggle-btn', { active: filters.gameFilter === option.value }]"
            >
              {{ option.shortLabel }}
            </button>
          </div>
        </div>

        <div class="filter-item currency-filter">
          <label>Currency</label>
          <div class="toggle-group">
            <button
              v-for="option in currencyFilterOptions"
              :key="option.value"
              @click="filters.currencyFilter = option.value"
              :class="['toggle-btn', { active: filters.currencyFilter === option.value }]"
            >
              {{ option.shortLabel }}
            </button>
          </div>
        </div>
      </div>
    </div>



    <div v-if="isLoading" class="loading">
      Loading transactions...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
      <button @click="fetchTransactions()" class="btn btn-primary retry-btn">
        Retry
      </button>
    </div>

    <div v-else-if="transactions.length === 0" class="empty-state">
      <p>No transactions found for the selected filters.</p>
    </div>

    <div v-else class="transactions-container">
      <div class="transaction-table">
        <div class="table-header">
          <div class="col-date">Date</div>
          <div class="col-type">Type</div>
          <div class="col-amount">Amount</div>
          <div class="col-currency">Currency</div>
          <div class="col-balance">Balance After</div>
        </div>

        <div
          v-for="transaction in transactions"
          :key="transaction.id"
          class="transaction-row"
        >
          <div class="col-date">
            {{ formatDate(transaction.created_at) }}
          </div>
          <div class="col-type">
            <span class="type-badge" :class="getTransactionTypeClass(transaction.type)">
              {{ transaction.type }}
            </span>
          </div>
          <div class="col-amount" :class="getTransactionTypeClass(transaction.type)">
            {{ formatAmount(transaction.amount_formatted, transaction.currency.symbol) }}
          </div>
          <div class="col-currency">
            {{ transaction.currency.code }}
          </div>
          <div class="col-balance">
            {{ transaction.currency.symbol }}{{ transaction.balance_after_formatted.toFixed(2) }}
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination">
        <div class="pagination-group">
          <!-- First Page -->
          <button
            @click="goToFirstPage"
            :disabled="currentPage <= 1"
            class="page-btn"
          >
            first
          </button>

          <!-- Previous Page -->
          <button
            @click="goToPreviousPage"
            :disabled="currentPage <= 1"
            class="page-btn"
          >
            previous
          </button>

          <!-- Page Numbers -->
          <template v-for="page in pageNumbers" :key="page">
            <button
              v-if="typeof page === 'number'"
              @click="goToPage(page)"
              :class="['page-btn', { active: page === currentPage }]"
            >
              {{ page }}
            </button>
            <span v-else class="page-ellipsis">{{ page }}</span>
          </template>

          <!-- Next Page -->
          <button
            @click="goToNextPage"
            :disabled="currentPage >= totalPages"
            class="page-btn"
          >
            next
          </button>

          <!-- Last Page -->
          <button
            @click="goToLastPage"
            :disabled="currentPage >= totalPages"
            class="page-btn"
          >
            last
          </button>
        </div>

        <div class="page-info">
          {{ pagination.total }} total
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.transaction-list {
  background: transparent;
}

/* Filters Section */
.filters-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
}



.filters-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: end;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-item label {
  font-weight: 500;
  color: #495057;
  font-size: 0.75rem;
  margin-bottom: 0.1rem;
}

.filter-select {
  padding: 0.3rem 0.4rem;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 0.75rem;
  background: white;
  color: #495057;
  transition: all 0.2s ease;
  cursor: pointer;
  min-width: 0;
  width: 100%;
}

.filter-select:focus {
  outline: none;
  border-color: #42b883;
  box-shadow: 0 0 0 3px rgba(66, 184, 131, 0.1);
}

.filter-select:hover {
  border-color: #42b883;
}

/* Time Toggle Styles */
.time-filter {
  grid-column: 1;
}

.time-toggle-group {
  display: inline-flex;
  background: #e9ecef;
  border-radius: 4px;
  padding: 2px;
  gap: 1px;
  width: fit-content;
}

.time-toggle {
  flex: 0 0 auto;
  padding: 0.25rem 0.4rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 0.7rem;
  font-weight: 500;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: lowercase;
  min-width: fit-content;
}

.time-toggle:hover {
  background: rgba(66, 184, 131, 0.1);
  color: #42b883;
}

.time-toggle.active {
  background: #42b883;
  color: white;
  box-shadow: 0 1px 2px rgba(66, 184, 131, 0.3);
}

/* General Toggle Styles for Game and Currency */
.toggle-group {
  display: inline-flex;
  background: #e9ecef;
  border-radius: 4px;
  padding: 2px;
  gap: 1px;
  width: fit-content;
}

.toggle-btn {
  flex: 0 0 auto;
  padding: 0.25rem 0.4rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 0.7rem;
  font-weight: 500;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: lowercase;
  min-width: fit-content;
}

.toggle-btn:hover {
  background: rgba(66, 184, 131, 0.1);
  color: #42b883;
}

.toggle-btn.active {
  background: #42b883;
  color: white;
  box-shadow: 0 1px 2px rgba(66, 184, 131, 0.3);
}



.total-count {
  color: #6c757d;
  font-size: 0.8rem;
  font-weight: 500;
}

.loading, .error, .empty-state {
  text-align: center;
  padding: 3rem 1.5rem;
  color: #6c757d;
}

.transactions-container {
  padding: 0;
}

.error {
  color: #dc3545;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.retry-btn:hover {
  background-color: #369870;
}

.transaction-table {
  width: 100%;
}

.table-header, .transaction-row {
  display: grid;
  grid-template-columns: 2fr 1.2fr 1.2fr 0.8fr 1.2fr;
  gap: 0.5rem;
  padding: 0.4rem 0.5rem;
  align-items: center;
}

.table-header {
  background: #f8f9fa;
  border-radius: 4px;
  font-weight: 600;
  color: #495057;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.transaction-row {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
  font-size: 0.8rem;
}

.transaction-row:hover {
  background-color: #f8f9fa;
}

.transaction-row:last-child {
  border-bottom: none;
}

.type-badge {
  padding: 0.15rem 0.3rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: capitalize;
}

.type-badge.positive {
  background: #d4edda;
  color: #155724;
}

.type-badge.negative {
  background: #f8d7da;
  color: #721c24;
}

.type-badge.neutral {
  background: #e2e3e5;
  color: #383d41;
}

.col-amount.positive {
  color: #28a745;
  font-weight: 600;
}

.col-amount.negative {
  color: #dc3545;
  font-weight: 600;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
}

.pagination-group {
  display: inline-flex;
  background: #e9ecef;
  border-radius: 4px;
  padding: 2px;
  gap: 1px;
}

.page-btn {
  flex: 0 0 auto;
  padding: 0.4rem 0.6rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 0.7rem;
  font-weight: 500;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
}

.page-btn:hover:not(:disabled) {
  background: rgba(66, 184, 131, 0.1);
  color: #42b883;
}

.page-btn.active {
  background: #42b883;
  color: white;
  box-shadow: 0 1px 2px rgba(66, 184, 131, 0.3);
}

.page-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.page-ellipsis {
  padding: 0.25rem 0.2rem;
  color: #6c757d;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
}

.page-info {
  color: #6c757d;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 1024px) {
  .filters-grid {
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .filters-section {
    padding: 1rem;
  }

  .filters-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .filters-grid {
    gap: 0.5rem;
  }

  .time-toggle, .toggle-btn {
    padding: 0.3rem 0.5rem;
    font-size: 0.7rem;
  }

  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }

  .page-btn {
    padding: 0.5rem 0.7rem;
    font-size: 0.65rem;
  }



  .table-header, .transaction-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-header {
    display: none;
  }

  .transaction-row {
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    padding: 1rem;
  }

  .transaction-row > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .transaction-row > div::before {
    content: attr(data-label);
    font-weight: 600;
    color: #495057;
  }
}
</style>
