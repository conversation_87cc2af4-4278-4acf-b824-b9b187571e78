<script setup lang="ts">
import { ref, watch } from 'vue'
import axios from 'axios'

interface GameSummary {
  [currencyCode: string]: {
    total_bet: number
    total_bet_formatted: number
    total_profit: number
    total_profit_formatted: number
    games_count: number
    wins_count: number
    win_rate: number
    currency: {
      code: string
      symbol: string
      decimals: number
    }
  }
}

interface BettingSummaryData {
  dice: GameSummary
  // Add other games here as they are implemented
}

const props = defineProps<{
  dateRange: string
}>()

const emit = defineEmits<{
  'update:dateRange': [value: string]
}>()

// Date range options for toggle
const dateRangeOptions = [
  { value: 'all', label: 'All Time', shortLabel: 'all' },
  { value: '1h', label: 'Last Hour', shortLabel: '1h' },
  { value: '1d', label: 'Last Day', shortLabel: '1d' },
  { value: '30d', label: 'Last 30 Days', shortLabel: '30d' },
]

const summaryData = ref<BettingSummaryData | null>(null)
const isLoading = ref(false)
const error = ref('')

// Get all unique currencies from the summary data
const currencies = ref<string[]>([])

const fetchSummary = async () => {
  isLoading.value = true
  error.value = ''

  try {
    const params = new URLSearchParams({
      date_range: props.dateRange,
    })

    const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/game-betting-summary?${params}`)

    if (response.data.success) {
      summaryData.value = response.data.data

      // Extract unique currencies
      const currencySet = new Set<string>()
      Object.values(response.data.data).forEach((gameData: any) => {
        Object.keys(gameData).forEach(currencyCode => {
          currencySet.add(currencyCode)
        })
      })
      currencies.value = Array.from(currencySet).sort()
    } else {
      error.value = 'Failed to load betting summary'
    }
  } catch (err: any) {
    console.error('Failed to fetch betting summary:', err)
    error.value = 'Failed to load betting summary'
  } finally {
    isLoading.value = false
  }
}

// Watch for date range changes
watch(() => props.dateRange, () => {
  fetchSummary()
}, { immediate: true })

const formatCurrency = (amount: number, symbol: string) => {
  return `${symbol}${amount.toFixed(2)}`
}

const getGameDisplayName = (gameKey: string) => {
  const gameNames: { [key: string]: string } = {
    dice: 'Dice',
    // Add other games here
  }
  return gameNames[gameKey] || gameKey
}

const getDateRangeLabel = (range: string) => {
  const labels: { [key: string]: string } = {
    all: 'All Time',
    '1h': 'Last Hour',
    '1d': 'Last Day',
    '30d': 'Last 30 Days',
  }
  return labels[range] || range
}

const updateDateRange = (newRange: string) => {
  emit('update:dateRange', newRange)
}
</script>

<template>
  <div class="game-betting-summary">
    <div class="summary-header">
      <h3>Betting Summary</h3>
      <div class="date-filter">
        <label>Time Period</label>
        <div class="time-toggle-group">
          <button
            v-for="option in dateRangeOptions"
            :key="option.value"
            @click="updateDateRange(option.value)"
            :class="['time-toggle', { active: dateRange === option.value }]"
          >
            {{ option.shortLabel }}
          </button>
        </div>
      </div>
    </div>

    <div v-if="isLoading" class="loading">
      Loading betting summary...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
      <button @click="fetchSummary" class="btn btn-primary retry-btn">
        Retry
      </button>
    </div>

    <div v-else-if="!summaryData || Object.keys(summaryData).length === 0" class="empty-state">
      <p>No betting activity found for the selected time period.</p>
    </div>

    <div v-else class="summary-content">
      <!-- Summary Table -->
      <div class="summary-table">
        <div class="table-header">
          <div class="game-col">Game</div>
          <div class="metric-col">Metric</div>
          <div
            v-for="currency in currencies"
            :key="currency"
            class="currency-col"
          >
            {{ currency }}
          </div>
        </div>

        <template v-for="(gameData, gameKey) in summaryData" :key="gameKey">
          <!-- Total Bet Row -->
          <div class="table-row">
            <div class="game-col game-name">{{ getGameDisplayName(gameKey) }}</div>
            <div class="metric-col">Total Bet</div>
            <div
              v-for="currency in currencies"
              :key="`${gameKey}-bet-${currency}`"
              class="currency-col"
            >
              <span v-if="gameData[currency]" class="amount negative">
                {{ formatCurrency(gameData[currency].total_bet_formatted, gameData[currency].currency.symbol) }}
              </span>
              <span v-else class="no-data">-</span>
            </div>
          </div>

          <!-- Total Profit Row -->
          <div class="table-row">
            <div class="game-col"></div>
            <div class="metric-col">Total Profit</div>
            <div
              v-for="currency in currencies"
              :key="`${gameKey}-profit-${currency}`"
              class="currency-col"
            >
              <span
                v-if="gameData[currency]"
                class="amount"
                :class="gameData[currency].total_profit_formatted >= 0 ? 'positive' : 'negative'"
              >
                {{ formatCurrency(gameData[currency].total_profit_formatted, gameData[currency].currency.symbol) }}
              </span>
              <span v-else class="no-data">-</span>
            </div>
          </div>

          <!-- Games Count Row -->
          <div class="table-row">
            <div class="game-col"></div>
            <div class="metric-col">Games Played</div>
            <div
              v-for="currency in currencies"
              :key="`${gameKey}-count-${currency}`"
              class="currency-col"
            >
              <span v-if="gameData[currency]" class="count">
                {{ gameData[currency].games_count }}
              </span>
              <span v-else class="no-data">-</span>
            </div>
          </div>

          <!-- Win Rate Row -->
          <div class="table-row last-row">
            <div class="game-col"></div>
            <div class="metric-col">Win Rate</div>
            <div
              v-for="currency in currencies"
              :key="`${gameKey}-winrate-${currency}`"
              class="currency-col"
            >
              <span v-if="gameData[currency]" class="win-rate">
                {{ gameData[currency].win_rate.toFixed(1) }}%
              </span>
              <span v-else class="no-data">-</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.game-betting-summary {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: end;
  margin-bottom: 1rem;
  gap: 1rem;
}

.summary-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.date-filter {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-filter label {
  font-weight: 500;
  color: #495057;
  font-size: 0.75rem;
}

.time-toggle-group {
  display: inline-flex;
  background: #e9ecef;
  border-radius: 4px;
  padding: 2px;
  gap: 1px;
  width: fit-content;
}

.time-toggle {
  flex: 0 0 auto;
  padding: 0.25rem 0.4rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 0.7rem;
  font-weight: 500;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: lowercase;
  min-width: fit-content;
}

.time-toggle:hover {
  background: rgba(66, 184, 131, 0.1);
  color: #42b883;
}

.time-toggle.active {
  background: #42b883;
  color: white;
  box-shadow: 0 1px 2px rgba(66, 184, 131, 0.3);
}

.loading, .error, .empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  color: #dc3545;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.retry-btn:hover {
  background-color: #369870;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
}

.table-header, .table-row {
  display: grid;
  grid-template-columns: 1fr 1fr repeat(var(--currency-count, 3), 1fr);
  gap: 1rem;
  padding: 0.75rem;
  align-items: center;
}

.table-header {
  background: #f8f9fa;
  border-radius: 4px;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.table-row {
  border-bottom: 1px solid #e9ecef;
}

.table-row.last-row {
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 1rem;
}

.game-name {
  font-weight: 600;
  color: #495057;
}

.metric-col {
  color: #666;
  font-size: 0.9rem;
}

.amount.positive {
  color: #28a745;
  font-weight: 600;
}

.amount.negative {
  color: #dc3545;
  font-weight: 600;
}

.count {
  color: #495057;
  font-weight: 500;
}

.win-rate {
  color: #17a2b8;
  font-weight: 500;
}

.no-data {
  color: #adb5bd;
  font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
  .table-header, .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-header {
    display: none;
  }

  .table-row {
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    padding: 1rem;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .time-toggle {
    padding: 0.3rem 0.5rem;
    font-size: 0.7rem;
  }
}
</style>
