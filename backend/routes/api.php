<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BalanceController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\DiceGameController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TransactionNoteController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Test auth route
Route::middleware('auth:sanctum')->get('/test-auth', function (Request $request) {
    $user = $request->user();
    return response()->json([
        'authenticated' => !!$user,
        'user_id' => $user ? $user->id : null,
        'username' => $user ? $user->username : null
    ]);
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

// Message routes
Route::get('/messages/history', [MessageController::class, 'history']); // Public route for chat history


Route::middleware('auth:sanctum')->group(function () {
    Route::post('/messages', [MessageController::class, 'send']);
});

// Profile routes
Route::middleware('auth:sanctum')->group(function () {
    Route::put('/profile', [ProfileController::class, 'updateProfile']);
    Route::put('/profile/password', [ProfileController::class, 'updatePassword']);
    Route::post('/profile/image', [ProfileController::class, 'updateProfileImage']);
    Route::delete('/profile/image', [ProfileController::class, 'deleteProfileImage']);
});

// Currency routes (public)
Route::get('/currencies', [CurrencyController::class, 'index']);
Route::get('/currencies/{currency}', [CurrencyController::class, 'show']);

// Balance and transaction routes (authenticated)
Route::middleware('auth:sanctum')->group(function () {
    // Balance operations
    Route::get('/balances', [BalanceController::class, 'index']);
    Route::get('/balances/{currency}', [BalanceController::class, 'show']);
    Route::get('/user/balance', [BalanceController::class, 'userBalance']);
    Route::post('/transactions', [BalanceController::class, 'processTransaction']);
    Route::get('/transactions/{currency}', [BalanceController::class, 'transactions']);
    Route::get('/transactions', [BalanceController::class, 'allTransactions']);
    Route::get('/game-betting-summary', [BalanceController::class, 'gameBettingSummary']);

    // Transaction notes
    Route::get('/transactions/{transaction}/notes', [TransactionNoteController::class, 'index']);
    Route::post('/transactions/{transaction}/notes', [TransactionNoteController::class, 'store']);
    Route::get('/transactions/{transaction}/notes/{note}', [TransactionNoteController::class, 'show']);
    Route::put('/transactions/{transaction}/notes/{note}', [TransactionNoteController::class, 'update']);
    Route::delete('/transactions/{transaction}/notes/{note}', [TransactionNoteController::class, 'destroy']);
    Route::get('/notes/by-tag', [TransactionNoteController::class, 'getByTag']);

    // Dice game routes
    Route::post('/dice/roll', [DiceGameController::class, 'roll']);
    Route::post('/dice/calculate', [DiceGameController::class, 'calculate']);
    Route::post('/dice/minimum-bet', [DiceGameController::class, 'minimumBet']);
    Route::get('/dice/config', [DiceGameController::class, 'config']);
});
